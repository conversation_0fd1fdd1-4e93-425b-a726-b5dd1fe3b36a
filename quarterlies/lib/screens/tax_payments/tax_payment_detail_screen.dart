import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'tax_payment_form_screen.dart';

class TaxPaymentDetailScreen extends StatefulWidget {
  final String taxPaymentId;

  const TaxPaymentDetailScreen({super.key, required this.taxPaymentId});

  @override
  State<TaxPaymentDetailScreen> createState() => _TaxPaymentDetailScreenState();
}

class _TaxPaymentDetailScreenState extends State<TaxPaymentDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  String? _errorMessage;
  TaxPayment? _taxPayment;

  @override
  void initState() {
    super.initState();
    _loadTaxPaymentDetails();
  }

  Future<void> _loadTaxPaymentDetails() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTaxPaymentDetails',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final taxPayment = await _supabaseService.getTaxPaymentById(
            widget.taxPaymentId,
          );

          if (!mounted) return;

          setState(() {
            _taxPayment = taxPayment;
          });
        },
        message: 'Loading tax payment details...',
        errorMessage: 'Failed to load tax payment details',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load tax payment details: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deleteTaxPayment() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: ResponsiveSubtitle('Confirm Delete'),
                content: ResponsiveBody(
                  'Are you sure you want to delete this tax payment? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    style: TextButton.styleFrom(
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                    ),
                    child: ResponsiveBody('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    style: TextButton.styleFrom(
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                    ),
                    child: ResponsiveBody(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (!confirmed || !mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'deleteTaxPayment',
        () async {
          await _supabaseService.deleteTaxPayment(widget.taxPaymentId);
          if (mounted) {
            // Show success feedback
            ErrorDisplay.showSuccess(
              context,
              'Tax payment deleted successfully',
            );
            Navigator.pop(context); // Return to the list screen
          }
        },
        message: 'Deleting tax payment...',
        errorMessage: 'Failed to delete tax payment',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to delete tax payment: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle('Tax Payment Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading =
                  loadingProvider.isLoading('loadTaxPaymentDetails') ||
                  loadingProvider.isLoading('deleteTaxPayment');

              return isLoading || _taxPayment == null
                  ? const SizedBox.shrink()
                  : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.edit,
                          size: spacing.ResponsiveSpacing.getIconSize(context),
                        ),
                        onPressed:
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => TaxPaymentFormScreen(
                                      taxPayment: _taxPayment,
                                    ),
                              ),
                            ).then((_) => _loadTaxPaymentDetails()),
                        tooltip: 'Edit payment',
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.delete,
                          size: spacing.ResponsiveSpacing.getIconSize(context),
                        ),
                        onPressed: _deleteTaxPayment,
                        tooltip: 'Delete payment',
                      ),
                    ],
                  );
            },
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading(
              'loadTaxPaymentDetails',
            );

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading tax payment details...',
                    size: 32.0,
                    showOfflineStatus: true,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadTaxPaymentDetails,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            double.infinity,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: ResponsiveBody('Retry'),
                      ),
                    ],
                  ),
                )
                : _taxPayment == null
                ? Center(child: ResponsiveBody('Tax payment not found'))
                : Consumer<DisplaySettingsProvider>(
                  builder: (context, displayProvider, child) {
                    return SingleChildScrollView(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: displayProvider.isOfficeMode ? 12.0 : 16.0,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Tax payment details section
                          AdaptiveDetailSection(
                            title: 'Tax Payment Details',
                            icon: Icons.account_balance,
                            alwaysExpandedInOffice: true,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: ResponsiveSubtitle(
                                      _taxPayment!.taxPeriod,
                                    ),
                                  ),
                                  Container(
                                    padding: spacing
                                        .ResponsiveSpacing.getPadding(
                                      context,
                                      base:
                                          displayProvider.isOfficeMode ? 8 : 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      borderRadius: BorderRadius.circular(
                                        spacing
                                            .ResponsiveSpacing.getBorderRadius(
                                          context,
                                          base: 16,
                                        ),
                                      ),
                                    ),
                                    child: ResponsiveLabel(
                                      NumberFormat.currency(
                                        symbol: '\$',
                                      ).format(_taxPayment!.amount),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: displayProvider.isOfficeMode ? 12 : 16,
                                ),
                              ),

                              if (displayProvider.isOfficeMode) ...[
                                // Office Mode: Compact grid layout
                                Row(
                                  children: [
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Payment Date',
                                        value: DateFormat.yMMMMd().format(
                                          _taxPayment!.date,
                                        ),
                                        icon: Icons.calendar_today,
                                      ),
                                    ),
                                    SizedBox(
                                      width: spacing
                                          .ResponsiveSpacing.getSpacing(
                                        context,
                                        base: 16,
                                      ),
                                    ),
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Amount',
                                        value: NumberFormat.currency(
                                          symbol: '\$',
                                        ).format(_taxPayment!.amount),
                                        icon: Icons.attach_money,
                                      ),
                                    ),
                                  ],
                                ),
                                if (_taxPayment!.paymentMethod != null) ...[
                                  SizedBox(
                                    height: spacing
                                        .ResponsiveSpacing.getSpacing(
                                      context,
                                      base: 8,
                                    ),
                                  ),
                                  AdaptiveInfoRow(
                                    label: 'Payment Method',
                                    value: _taxPayment!.paymentMethod!,
                                    icon: Icons.credit_card,
                                  ),
                                ],
                                if (_taxPayment!.confirmationNumber !=
                                    null) ...[
                                  SizedBox(
                                    height: spacing
                                        .ResponsiveSpacing.getSpacing(
                                      context,
                                      base: 8,
                                    ),
                                  ),
                                  AdaptiveInfoRow(
                                    label: 'Confirmation Number',
                                    value: _taxPayment!.confirmationNumber!,
                                    icon: Icons.confirmation_number,
                                  ),
                                ],
                              ] else ...[
                                // Field Mode: Existing layout
                                _buildDetailRow(
                                  'Payment Date',
                                  DateFormat.yMMMMd().format(_taxPayment!.date),
                                ),
                                if (_taxPayment!.paymentMethod != null)
                                  _buildDetailRow(
                                    'Payment Method',
                                    _taxPayment!.paymentMethod!,
                                  ),
                                if (_taxPayment!.confirmationNumber != null)
                                  _buildDetailRow(
                                    'Confirmation Number',
                                    _taxPayment!.confirmationNumber!,
                                  ),
                              ],
                            ],
                          ),

                          if (_taxPayment!.notes != null &&
                              _taxPayment!.notes!.isNotEmpty) ...[
                            ResponsiveSubtitle('Notes'),
                            SizedBox(
                              height: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8,
                              ),
                            ),
                            Card(
                              child: Padding(
                                padding: spacing.ResponsiveSpacing.getPadding(
                                  context,
                                  base: 16.0,
                                ),
                                child: ResponsiveBody(_taxPayment!.notes!),
                              ),
                            ),
                          ],

                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 16,
                            ),
                          ),
                          ResponsiveSubtitle('Payment Information'),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 8,
                            ),
                          ),
                          Card(
                            child: Padding(
                              padding: spacing.ResponsiveSpacing.getPadding(
                                context,
                                base: 16.0,
                              ),
                              child: Column(
                                children: [
                                  _buildDetailRow(
                                    'Created',
                                    DateFormat.yMMMd().add_jm().format(
                                      _taxPayment!.createdAt,
                                    ),
                                  ),
                                  _buildDetailRow(
                                    'Last Updated',
                                    DateFormat.yMMMd().add_jm().format(
                                      _taxPayment!.updatedAt,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
          },
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: spacing.ResponsiveSpacing.getPadding(context, base: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: spacing.ResponsiveSpacing.getSpacing(context, base: 140),
            child: ResponsiveLabel(
              label,
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey),
            ),
          ),
          Expanded(child: ResponsiveBody(value)),
        ],
      ),
    );
  }
}
