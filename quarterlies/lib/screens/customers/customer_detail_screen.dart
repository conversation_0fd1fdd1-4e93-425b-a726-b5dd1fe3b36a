import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'customer_form_screen.dart';
import 'package:quarterlies/screens/jobs/job_detail_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_detail_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class CustomerDetailScreen extends StatefulWidget {
  final String customerId;

  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with SingleTickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  late TabController _tabController;
  Customer? _customer;
  Map<String, dynamic>? _customerData;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCustomerData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomerData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadCustomerData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final customer = await _supabaseService.getCustomerById(
            widget.customerId,
          );
          final customerData = await _supabaseService.getCustomerDataOptimized(
            widget.customerId,
          );

          if (!mounted) return;

          setState(() {
            _customer = customer;
            _customerData = customerData;
          });
        },
        message: 'Loading customer data...',
        errorMessage: 'Failed to load customer data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load customer data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deleteCustomer() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this customer? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (confirmed) {
      try {
        await _supabaseService.deleteCustomer(widget.customerId);
        if (mounted) {
          // Show success feedback
          ErrorDisplay.showSuccess(context, 'Customer deleted successfully');
          Navigator.pop(context, true); // Return true to indicate deletion
        }
      } catch (e) {
        if (mounted) {
          // Use centralized error handling
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'deleteCustomer',
              'customerId': widget.customerId,
            },
          );
          ErrorHandler.logError(appError);
          ErrorDisplay.showSnackBar(context, appError);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(_customer?.name ?? 'Customer Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          IconButton(
            icon: Icon(
              Icons.edit,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed:
                _customer == null
                    ? null
                    : () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  CustomerFormScreen(customer: _customer),
                        ),
                      );
                      if (result == true) {
                        _loadCustomerData();
                      }
                    },
          ),
          IconButton(
            icon: Icon(
              Icons.delete,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed: _customer == null ? null : _deleteCustomer,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Details'),
            Tab(text: 'Jobs'),
            Tab(text: 'Invoices'),
          ],
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
        ),
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadCustomerData');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading customer data...',
                    size: 32.0,
                    showOfflineStatus: true,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadCustomerData,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            0,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: const ResponsiveBody('Retry'),
                      ),
                    ],
                  ),
                )
                : _customer == null
                ? const Center(child: ResponsiveBody('Customer not found'))
                : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildDetailsTab(),
                    _buildJobsTab(),
                    _buildInvoicesTab(),
                  ],
                );
          },
        ),
      ),
    );
  }

  Widget _buildDetailsTab() {
    if (_customer == null) {
      return const Center(child: Text('Customer details not available'));
    }

    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return SingleChildScrollView(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Contact Information Section
              AdaptiveDetailSection(
                title: 'Contact Information',
                icon: Icons.contact_phone,
                alwaysExpandedInOffice: true,
                children: [
                  AdaptiveInfoRow(
                    label: 'Email',
                    value: _customer!.email ?? 'Not provided',
                    icon: Icons.email,
                  ),
                  AdaptiveInfoRow(
                    label: 'Phone',
                    value: _customer!.phone ?? 'Not provided',
                    icon: Icons.phone,
                  ),
                  AdaptiveInfoRow(
                    label: 'Address',
                    value: _buildFullAddress(),
                    icon: Icons.location_on,
                  ),
                ],
              ),

              // Customer Statistics (Office Mode only)
              if (displayProvider.isOfficeMode && _customerData != null) ...[
                AdaptiveDetailSection(
                  title: 'Customer Statistics',
                  icon: Icons.analytics,
                  alwaysExpandedInOffice: true,
                  children: [
                    AdaptiveInfoRow(
                      label: 'Total Jobs',
                      value:
                          '${(_customerData!['jobs_with_invoices'] as List?)?.length ?? 0}',
                      icon: Icons.work,
                    ),
                    AdaptiveInfoRow(
                      label: 'Total Invoices',
                      value: '${_getTotalInvoiceCount()}',
                      icon: Icons.receipt,
                    ),
                    AdaptiveInfoRow(
                      label: 'Total Revenue',
                      value: '\$${_getTotalRevenue().toStringAsFixed(2)}',
                      icon: Icons.attach_money,
                    ),
                    AdaptiveInfoRow(
                      label: 'Customer Since',
                      value: _formatDate(_customer!.createdAt),
                      icon: Icons.calendar_today,
                    ),
                  ],
                ),
              ],

              // Notes Section
              AdaptiveDetailSection(
                title: 'Notes',
                icon: Icons.note,
                alwaysExpandedInOffice:
                    displayProvider.isOfficeMode &&
                    (_customer!.notes == null || _customer!.notes!.isEmpty),
                children: [
                  Padding(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 8 : 12,
                    ),
                    child: Text(
                      _customer!.notes ?? 'No notes available',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 13 : 14,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildJobsTab() {
    final jobsWithInvoices =
        (_customerData?['jobs_with_invoices'] as List<dynamic>?)
            ?.cast<Map<String, dynamic>>() ??
        [];

    return jobsWithInvoices.isEmpty
        ? const Center(child: Text('No jobs found for this customer'))
        : ListView.builder(
          itemCount: jobsWithInvoices.length,
          itemBuilder: (context, index) {
            final job = jobsWithInvoices[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ListTile(
                title: Text(
                  job['title'] ?? 'Untitled Job',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  'Status: ${job['status'] ?? 'Unknown'} • Est. Price: \$${job['estimated_price'] ?? 0.0}',
                ),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => JobDetailScreen(jobId: job['id']),
                    ),
                  );
                },
              ),
            );
          },
        );
  }

  Widget _buildInvoicesTab() {
    final jobsWithInvoices =
        (_customerData?['jobs_with_invoices'] as List<dynamic>?)
            ?.cast<Map<String, dynamic>>() ??
        [];
    final allInvoices = <Map<String, dynamic>>[];

    for (final job in jobsWithInvoices) {
      final invoices =
          (job['invoices'] as List<dynamic>?)?.cast<Map<String, dynamic>>() ??
          [];
      for (final invoice in invoices) {
        invoice['job_title'] = job['title'];
        allInvoices.add(invoice);
      }
    }

    return allInvoices.isEmpty
        ? const Center(child: Text('No invoices found for this customer'))
        : ListView.builder(
          itemCount: allInvoices.length,
          itemBuilder: (context, index) {
            final invoice = allInvoices[index];
            final invoiceDate = DateTime.tryParse(
              invoice['invoice_date'] ?? '',
            );
            final formattedDate =
                invoiceDate != null
                    ? '${invoiceDate.month}/${invoiceDate.day}/${invoiceDate.year}'
                    : 'Unknown Date';

            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ListTile(
                title: Text(
                  'Invoice #${invoice['invoice_number'] ?? 'Unknown'}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  '${invoice['job_title'] ?? 'Unknown Job'} • $formattedDate • \$${invoice['total_amount'] ?? 0.0}',
                ),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              InvoiceDetailScreen(invoiceId: invoice['id']),
                    ),
                  );
                },
              ),
            );
          },
        );
  }

  int _getTotalInvoiceCount() {
    if (_customerData == null) return 0;

    final jobsWithInvoices =
        (_customerData!['jobs_with_invoices'] as List<dynamic>?)
            ?.cast<Map<String, dynamic>>() ??
        [];

    int totalInvoices = 0;
    for (final job in jobsWithInvoices) {
      final invoices =
          (job['invoices'] as List<dynamic>?)?.cast<Map<String, dynamic>>() ??
          [];
      totalInvoices += invoices.length;
    }
    return totalInvoices;
  }

  double _getTotalRevenue() {
    if (_customerData == null) return 0.0;

    final jobsWithInvoices =
        (_customerData!['jobs_with_invoices'] as List<dynamic>?)
            ?.cast<Map<String, dynamic>>() ??
        [];

    double totalRevenue = 0.0;
    for (final job in jobsWithInvoices) {
      final invoices =
          (job['invoices'] as List<dynamic>?)?.cast<Map<String, dynamic>>() ??
          [];
      for (final invoice in invoices) {
        totalRevenue += (invoice['total_amount'] as num?)?.toDouble() ?? 0.0;
      }
    }
    return totalRevenue;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _buildFullAddress() {
    if (_customer == null) return 'Not provided';

    final parts =
        [
          _customer!.address,
          _customer!.city,
          _customer!.state,
          _customer!.zipCode,
        ].where((part) => part != null && part.isNotEmpty).toList();

    return parts.isEmpty ? 'Not provided' : parts.join(', ');
  }
}
