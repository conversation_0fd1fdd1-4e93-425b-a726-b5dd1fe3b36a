import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/services/address_service.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class CustomerFormScreen extends StatefulWidget {
  final Customer? customer; // Null for new customer, non-null for editing

  const CustomerFormScreen({super.key, this.customer});

  @override
  State<CustomerFormScreen> createState() => _CustomerFormScreenState();
}

class _CustomerFormScreenState extends State<CustomerFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _notesController = TextEditingController();
  final _dataRepository = DataRepository();
  final _voiceRecordingService = VoiceRecordingService();
  final _addressService = AddressService(); // Added for address autocomplete
  List<AddressPrediction> _addressPredictions =
      []; // Stores address suggestions
  bool _isRecording = false;
  String? _errorMessage;
  bool _isOffline = false;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  bool get _isEditing => widget.customer != null;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    if (_isEditing) {
      // Populate form fields with existing customer data
      _nameController.text = widget.customer!.name;
      _emailController.text = widget.customer!.email ?? '';
      _phoneController.text = widget.customer!.phone ?? '';
      _addressController.text = widget.customer!.address ?? '';
      _cityController.text = widget.customer!.city ?? '';
      _stateController.text = widget.customer!.state ?? '';
      _zipCodeController.text = widget.customer!.zipCode ?? '';
      _notesController.text = widget.customer!.notes ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _notesController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'startRecording'},
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text specifically for customer information
        final extractedInfo = _voiceRecordingService.processCustomerVoiceInput(
          transcribedText,
        );

        setState(() {
          // Apply extracted information to form fields
          if (extractedInfo.containsKey('name')) {
            _nameController.text = extractedInfo['name'];
          }
          if (extractedInfo.containsKey('email')) {
            _emailController.text = extractedInfo['email'];
          }
          if (extractedInfo.containsKey('phone')) {
            _phoneController.text = extractedInfo['phone'];
          }
          if (extractedInfo.containsKey('address')) {
            _addressController.text = extractedInfo['address'];
          }
          if (extractedInfo.containsKey('city')) {
            _cityController.text = extractedInfo['city'];
          }
          if (extractedInfo.containsKey('state')) {
            _stateController.text = extractedInfo['state'];
          }
          if (extractedInfo.containsKey('zipCode')) {
            _zipCodeController.text = extractedInfo['zipCode'];
          }
          if (extractedInfo.containsKey('description')) {
            _notesController.text = extractedInfo['description'];
          }
        });

        // Show success feedback for voice processing
        if (mounted) {
          ErrorDisplay.showOperation(context, FeedbackMessages.voiceCompleted);
        }
      }
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'stopRecording'},
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveCustomer',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final userId = Supabase.instance.client.auth.currentUser!.id;

          if (_isEditing) {
            // Update existing customer
            final updatedCustomer = Customer(
              id: widget.customer!.id,
              userId: userId,
              name: _nameController.text.trim(),
              email:
                  _emailController.text.trim().isNotEmpty
                      ? _emailController.text.trim()
                      : null,
              phone:
                  _phoneController.text.trim().isNotEmpty
                      ? _phoneController.text.trim()
                      : null,
              address:
                  _addressController.text.trim().isNotEmpty
                      ? _addressController.text.trim()
                      : null,
              city:
                  _cityController.text.trim().isNotEmpty
                      ? _cityController.text.trim()
                      : null,
              state:
                  _stateController.text.trim().isNotEmpty
                      ? _stateController.text.trim()
                      : null,
              zipCode:
                  _zipCodeController.text.trim().isNotEmpty
                      ? _zipCodeController.text.trim()
                      : null,
              notes:
                  _notesController.text.trim().isNotEmpty
                      ? _notesController.text.trim()
                      : null,
              createdAt: widget.customer!.createdAt,
              updatedAt: DateTime.now(),
            );

            await _dataRepository.updateCustomer(updatedCustomer);
          } else {
            // Create new customer
            final newCustomer = Customer(
              userId: userId,
              name: _nameController.text.trim(),
              email:
                  _emailController.text.trim().isNotEmpty
                      ? _emailController.text.trim()
                      : null,
              phone:
                  _phoneController.text.trim().isNotEmpty
                      ? _phoneController.text.trim()
                      : null,
              address:
                  _addressController.text.trim().isNotEmpty
                      ? _addressController.text.trim()
                      : null,
              city:
                  _cityController.text.trim().isNotEmpty
                      ? _cityController.text.trim()
                      : null,
              state:
                  _stateController.text.trim().isNotEmpty
                      ? _stateController.text.trim()
                      : null,
              zipCode:
                  _zipCodeController.text.trim().isNotEmpty
                      ? _zipCodeController.text.trim()
                      : null,
              notes:
                  _notesController.text.trim().isNotEmpty
                      ? _notesController.text.trim()
                      : null,
            );

            await _dataRepository.addCustomer(newCustomer);
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'customer',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context, true); // Return true to indicate success
          }
        },
        message: _isEditing ? 'Updating customer...' : 'Adding customer...',
        errorMessage: 'Failed to save customer',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveCustomer',
            'isEditing': _isEditing,
            'customerName': _nameController.text.trim(),
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        // Show error dialog for critical errors
        if (appError.severity == ErrorSeverity.high ||
            appError.severity == ErrorSeverity.critical) {
          ErrorDisplay.showErrorDialog(
            context,
            appError,
            onRetry: () => _saveCustomer(),
          );
        } else {
          // Show snackbar for less critical errors
          ErrorDisplay.showSnackBar(
            context,
            appError,
            onRetry: () => _saveCustomer(),
          );
        }
      }
    }
  }

  void _onAddressChanged(String input) async {
    if (input.isNotEmpty) {
      final predictions = await _addressService.getAddressPredictions(input);
      setState(() {
        _addressPredictions = predictions;
      });
    } else {
      setState(() {
        _addressPredictions = [];
      });
    }
  }

  void _selectAddress(String address) {
    setState(() {
      _addressController.text = address;
      _addressPredictions = [];
    });
  }

  Widget _buildOfflineBanner() {
    return _isOffline
        ? Container(
          color: Colors.orange,
          padding: const EdgeInsets.all(8),
          child: const Text(
            'You are offline. Changes will be saved locally.',
            style: TextStyle(color: Colors.white),
          ),
        )
        : const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(_isEditing ? 'Edit Customer' : 'Add Customer'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Voice recording button in app bar
          IconButton(
            icon:
                _isRecording
                    ? Icon(
                      Icons.stop_circle,
                      color: Colors.red,
                      size: spacing.ResponsiveSpacing.getIconSize(context),
                    )
                    : Icon(
                      Icons.mic,
                      size: spacing.ResponsiveSpacing.getIconSize(context),
                    ),
            onPressed: _isRecording ? _stopRecording : _startRecording,
            tooltip:
                _isRecording ? 'Stop recording' : 'Add customer using voice',
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('saveCustomer');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Saving customer...',
                    size: 32.0,
                    showOfflineStatus: true,
                  ),
                )
                : Consumer<DisplaySettingsProvider>(
                  builder: (context, displayProvider, child) {
                    return SingleChildScrollView(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_errorMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.red.shade50,
                                    border: Border.all(
                                      color: Colors.red.shade200,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.error_outline,
                                        color: Colors.red.shade700,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          _errorMessage!,
                                          style: TextStyle(
                                            color: Colors.red.shade700,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.close),
                                        iconSize: 18,
                                        color: Colors.red.shade700,
                                        onPressed: () {
                                          setState(() {
                                            _errorMessage = null;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            _buildOfflineBanner(),
                            // Basic Information Section
                            AdaptiveFormSection(
                              title: 'Basic Information',
                              icon: Icons.person,
                              children: [
                                // Voice recording indicator/button
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const SizedBox(), // Spacer
                                    if (_isRecording)
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal:
                                              displayProvider.isOfficeMode
                                                  ? 8
                                                  : 12,
                                          vertical:
                                              displayProvider.isOfficeMode
                                                  ? 4
                                                  : 6,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.red.shade50,
                                          borderRadius: BorderRadius.circular(
                                            16,
                                          ),
                                          border: Border.all(
                                            color: Colors.red.shade200,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.mic,
                                              color: Colors.red,
                                              size:
                                                  displayProvider.isOfficeMode
                                                      ? 16
                                                      : 18,
                                            ),
                                            SizedBox(
                                              width:
                                                  displayProvider.isOfficeMode
                                                      ? 3
                                                      : 4,
                                            ),
                                            Text(
                                              'Recording...',
                                              style: TextStyle(
                                                color: Colors.red,
                                                fontSize:
                                                    displayProvider.isOfficeMode
                                                        ? 12
                                                        : 14,
                                              ),
                                            ),
                                            SizedBox(
                                              width:
                                                  displayProvider.isOfficeMode
                                                      ? 3
                                                      : 4,
                                            ),
                                            InkWell(
                                              onTap: _stopRecording,
                                              child: Icon(
                                                Icons.stop_circle,
                                                color: Colors.red,
                                                size:
                                                    displayProvider.isOfficeMode
                                                        ? 16
                                                        : 18,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    else
                                      Tooltip(
                                        message:
                                            'Add customer information using voice',
                                        child: ElevatedButton.icon(
                                          icon: Icon(
                                            Icons.mic,
                                            size:
                                                displayProvider.isOfficeMode
                                                    ? 16
                                                    : 18,
                                          ),
                                          label: Text(
                                            'Voice Input',
                                            style: TextStyle(
                                              fontSize:
                                                  displayProvider.isOfficeMode
                                                      ? 12
                                                      : 14,
                                            ),
                                          ),
                                          onPressed: _startRecording,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Colors.blue.shade50,
                                            foregroundColor:
                                                Colors.blue.shade700,
                                            elevation: 0,
                                            padding: EdgeInsets.symmetric(
                                              horizontal:
                                                  displayProvider.isOfficeMode
                                                      ? 8
                                                      : 12,
                                              vertical:
                                                  displayProvider.isOfficeMode
                                                      ? 4
                                                      : 6,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),

                                // Form fields
                                CustomTextField(
                                  controller: _nameController,
                                  labelText: 'Name',
                                  hintText: 'Enter customer name',
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'Please enter a name';
                                    }
                                    return null;
                                  },
                                ),

                                if (displayProvider.isOfficeMode) ...[
                                  // Office Mode: Email and Phone in same row
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomTextField(
                                          controller: _emailController,
                                          labelText: 'Email',
                                          hintText: 'Enter customer email',
                                          keyboardType:
                                              TextInputType.emailAddress,
                                          validator:
                                              (value) =>
                                                  InputValidators.validateEmail(
                                                    value,
                                                    required: true,
                                                  ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: CustomTextField(
                                          controller: _phoneController,
                                          labelText: 'Phone',
                                          hintText: 'Enter customer phone',
                                          keyboardType: TextInputType.phone,
                                          validator: (value) {
                                            if (value == null ||
                                                value.trim().isEmpty) {
                                              return 'Please enter a phone number';
                                            }
                                            final phoneRegex = RegExp(
                                              r'^\+?[0-9]{7,15}$',
                                            );
                                            if (!phoneRegex.hasMatch(value)) {
                                              return 'Please enter a valid phone number';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ] else ...[
                                  // Field Mode: Separate fields
                                  CustomTextField(
                                    controller: _emailController,
                                    labelText: 'Email',
                                    hintText: 'Enter customer email',
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return 'Please enter an email address';
                                      }
                                      final emailRegex = RegExp(
                                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                      );
                                      if (!emailRegex.hasMatch(value)) {
                                        return 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                  ),
                                  CustomTextField(
                                    controller: _phoneController,
                                    labelText: 'Phone',
                                    hintText: 'Enter customer phone',
                                    keyboardType: TextInputType.phone,
                                    validator:
                                        (value) =>
                                            InputValidators.validatePhone(
                                              value,
                                              required: true,
                                            ),
                                  ),
                                ],
                              ],
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.green.shade200,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.mail_outline,
                                        color: Colors.green.shade700,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Billing Address',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green.shade700,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Tooltip(
                                        message:
                                            'This is the customer\'s mailing/billing address. For job locations, use the Service Address in the job details.',
                                        child: Icon(
                                          Icons.info_outline,
                                          size: 16,
                                          color: Colors.green.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    'The billing address is where invoices and other correspondence will be sent. Job locations should be set in each job\'s service address.',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black54,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Stack(
                              children: [
                                TextFormField(
                                  controller: _addressController,
                                  decoration: InputDecoration(
                                    labelText: 'Billing Street Address',
                                    hintText:
                                        'Enter customer\'s billing address',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    filled: true,
                                    fillColor: Colors.grey[100],
                                  ),
                                  onChanged: _onAddressChanged,
                                ),
                                if (_addressPredictions.isNotEmpty)
                                  Positioned(
                                    top: 60,
                                    left: 0,
                                    right: 0,
                                    child: Material(
                                      elevation: 4,
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        itemCount: _addressPredictions.length,
                                        itemBuilder: (context, index) {
                                          final prediction =
                                              _addressPredictions[index];
                                          return ListTile(
                                            title: Text(prediction.description),
                                            onTap:
                                                () => _selectAddress(
                                                  prediction.description,
                                                ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: TextFormField(
                                    controller: _cityController,
                                    decoration: InputDecoration(
                                      labelText: 'Billing City',
                                      hintText: 'Enter city',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                          8.0,
                                        ),
                                      ),
                                      filled: true,
                                      fillColor: Colors.grey[100],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    controller: _stateController,
                                    decoration: InputDecoration(
                                      labelText: 'State',
                                      hintText: 'State',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                          8.0,
                                        ),
                                      ),
                                      filled: true,
                                      fillColor: Colors.grey[100],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _zipCodeController,
                              decoration: InputDecoration(
                                labelText: 'Billing ZIP Code',
                                hintText: 'Enter ZIP code',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Additional Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Notes field with voice recording button
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 12.0,
                              ),
                              child: Stack(
                                alignment: Alignment.centerRight,
                                children: [
                                  TextFormField(
                                    controller: _notesController,
                                    decoration: InputDecoration(
                                      labelText: 'Notes',
                                      labelStyle: TextStyle(
                                        fontSize: 16,
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                      ),
                                      hintText:
                                          'Enter any additional notes or tap mic to use voice',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                          12.0,
                                        ),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 18,
                                          ),
                                      suffixIcon:
                                          _isRecording
                                              ? Container(
                                                margin: const EdgeInsets.only(
                                                  right: 32,
                                                ),
                                                child: const Icon(
                                                  Icons.mic,
                                                  color: Colors.red,
                                                ),
                                              )
                                              : null,
                                    ),
                                    keyboardType: TextInputType.multiline,
                                    maxLines: 3,
                                  ),
                                  Positioned(
                                    right: 8,
                                    child: IconButton(
                                      icon: Icon(
                                        _isRecording ? Icons.stop : Icons.mic,
                                        color:
                                            _isRecording
                                                ? Colors.red
                                                : Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                      ),
                                      onPressed:
                                          _isRecording
                                              ? _stopRecording
                                              : _startRecording,
                                      tooltip:
                                          _isRecording
                                              ? 'Stop recording'
                                              : 'Record customer details',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            Row(
                              children: [
                                Expanded(
                                  child: Consumer<LoadingStateProvider>(
                                    builder: (context, loadingProvider, child) {
                                      return CustomButton(
                                        text:
                                            _isEditing
                                                ? 'Update Customer'
                                                : 'Add Customer',
                                        onPressed: _saveCustomer,
                                        isLoading: loadingProvider.isLoading(
                                          'saveCustomer',
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                if (!_isRecording)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 16.0),
                                    child: ElevatedButton.icon(
                                      icon: const Icon(Icons.mic),
                                      label: const Text('Voice Input'),
                                      onPressed: _startRecording,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            Theme.of(
                                              context,
                                            ).colorScheme.secondary,
                                        foregroundColor:
                                            Theme.of(
                                              context,
                                            ).colorScheme.onSecondary,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
          },
        ),
      ),
    );
  }
}
