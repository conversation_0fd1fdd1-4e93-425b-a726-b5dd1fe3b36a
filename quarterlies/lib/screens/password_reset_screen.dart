import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class PasswordResetScreen extends StatefulWidget {
  const PasswordResetScreen({super.key});

  @override
  State<PasswordResetScreen> createState() => _PasswordResetScreenState();
}

class _PasswordResetScreenState extends State<PasswordResetScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _authService = AuthService();
  String? _errorMessage;
  bool _resetEmailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _sendResetEmail() async {
    if (_formKey.currentState!.validate()) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'resetPassword',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.resetPassword(_emailController.text.trim());
            if (mounted) {
              setState(() {
                _resetEmailSent = true;
              });

              // Show success feedback
              ErrorDisplay.showSuccess(
                context,
                'Password reset link sent to ${_emailController.text.trim()}',
              );
            }
          },
          message: 'Sending reset link...',
          errorMessage: 'Failed to send reset link',
        );
      } catch (e) {
        if (mounted) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'resetPassword',
              'email': _emailController.text.trim(),
            },
          );
          ErrorHandler.logError(appError);

          setState(() {
            _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          });

          // Show error dialog for authentication errors
          if (appError.type == ErrorType.authentication) {
            ErrorDisplay.showErrorDialog(
              context,
              appError,
              onRetry: () => _sendResetEmail(),
            );
          } else {
            // Show snackbar for other errors
            ErrorDisplay.showSnackBar(
              context,
              appError,
              onRetry: () => _sendResetEmail(),
            );
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle('Reset Password'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
      ),
      body: ResponsiveLayout(
        child: SingleChildScrollView(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 24.0),
          child: _resetEmailSent ? _buildSuccessMessage() : _buildResetForm(),
        ),
      ),
    );
  }

  Widget _buildResetForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
          ),
          Icon(
            Icons.lock_reset,
            size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          ResponsiveTitle(
            'Forgot Your Password?',
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          ResponsiveBody(
            'Enter your email address and we\'ll send you a link to reset your password.',
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
          ),
          CustomTextField(
            controller: _emailController,
            labelText: 'Email',
            hintText: 'Enter your email address',
            keyboardType: TextInputType.emailAddress,
            validator: (value) => InputValidators.validateEmail(value),
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          if (_errorMessage != null)
            Padding(
              padding: EdgeInsets.only(
                bottom: spacing.ResponsiveSpacing.getSpacing(
                  context,
                  base: 16.0,
                ),
              ),
              child: ResponsiveBody(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              return CustomButton(
                text: 'Send Reset Link',
                onPressed: _sendResetEmail,
                isLoading: loadingProvider.isLoading('resetPassword'),
              );
            },
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          TextButton(
            onPressed: () {
              Navigator.pushReplacementNamed(context, '/login');
            },
            style: TextButton.styleFrom(
              minimumSize: Size(
                double.infinity,
                spacing.ResponsiveSpacing.getButtonHeight(context),
              ),
            ),
            child: ResponsiveBody('Back to Login'),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
        ),
        Icon(
          Icons.check_circle_outline,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
          color: Colors.green,
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
        ),
        ResponsiveTitle(
          'Reset Link Sent!',
          style: const TextStyle(fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
        ),
        ResponsiveBody(
          'We\'ve sent a password reset link to ${_emailController.text}. Please check your email and follow the instructions to reset your password.',
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
        ),
        CustomButton(
          text: 'Back to Login',
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/login');
          },
        ),
      ],
    );
  }
}
