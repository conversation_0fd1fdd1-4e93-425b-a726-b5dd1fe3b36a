import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class UpdatePasswordScreen extends StatefulWidget {
  const UpdatePasswordScreen({super.key});

  @override
  State<UpdatePasswordScreen> createState() => _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends State<UpdatePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = AuthService();
  String? _errorMessage;
  bool _passwordUpdated = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _updatePassword() async {
    if (_formKey.currentState!.validate()) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'updatePassword',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.updatePassword(_passwordController.text);

            if (mounted) {
              setState(() {
                _passwordUpdated = true;
              });

              // Show success feedback
              ErrorDisplay.showSuccess(
                context,
                'Password updated successfully',
              );
            }
          },
          message: 'Updating password...',
          errorMessage: 'Failed to update password',
        );
      } catch (e) {
        if (mounted) {
          setState(() {
            _errorMessage = e.toString();
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const ResponsiveTitle('Update Password'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: SafeArea(
              child: SingleChildScrollView(
                padding: spacing.ResponsiveSpacing.getPadding(
                  context,
                  base: 24.0,
                ),
                child:
                    _passwordUpdated
                        ? _buildSuccessMessage()
                        : _buildUpdateForm(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpdateForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
          ),
          Icon(
            Icons.lock_reset,
            size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          const ResponsiveTitle(
            'Create New Password',
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          const ResponsiveBody(
            'Your password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters.',
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
          ),
          CustomTextField(
            controller: _passwordController,
            labelText: 'New Password',
            hintText: 'Enter your new password',
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a new password';
              }
              if (!_authService.isPasswordStrong(value)) {
                return 'Password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters';
              }
              return null;
            },
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          CustomTextField(
            controller: _confirmPasswordController,
            labelText: 'Confirm Password',
            hintText: 'Confirm your new password',
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          if (_errorMessage != null)
            Padding(
              padding: EdgeInsets.only(
                bottom: spacing.ResponsiveSpacing.getSpacing(
                  context,
                  base: 16.0,
                ),
              ),
              child: ResponsiveBody(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              return CustomButton(
                text: 'Update Password',
                onPressed: _updatePassword,
                isLoading: loadingProvider.isLoading('updatePassword'),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
        ),
        Icon(
          Icons.check_circle_outline,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
          color: Colors.green,
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
        ),
        const ResponsiveTitle('Password Updated!', textAlign: TextAlign.center),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
        ),
        const ResponsiveBody(
          'Your password has been successfully updated. You can now log in with your new password.',
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 32),
        ),
        CustomButton(
          text: 'Go to Login',
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/login');
          },
        ),
      ],
    );
  }
}
