import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class PaymentFormScreen extends StatefulWidget {
  final Payment? payment;
  final String? jobId;
  final String? invoiceId;

  const PaymentFormScreen({
    super.key,
    this.payment,
    this.jobId,
    this.invoiceId,
  });

  @override
  State<PaymentFormScreen> createState() => _PaymentFormScreenState();
}

class _PaymentFormScreenState extends State<PaymentFormScreen> {
  final DataRepository _dataRepository = DataRepository();
  final VoiceRecordingService _voiceRecordingService = VoiceRecordingService();
  final SupabaseService _supabaseService = SupabaseService();
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _dateReceived = DateTime.now(); // This is paymentDate in the model
  String _paymentMethod = 'Cash';
  String? _selectedJobId;
  String? _selectedInvoiceId;

  List<Job> _jobs = [];
  List<Invoice> _invoices = [];
  dynamic _selectedInvoice; // Can be Invoice or Map<String, dynamic>
  bool _isRecording = false;
  String? _errorMessage;
  String? _voiceNoteUrl;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _initializeForm();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _voiceRecordingService.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show connectivity status feedback using centralized system
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(context, FeedbackMessages.workingOffline);
        }
      }
    });
  }

  Future<void> _initializeForm() async {
    if (!mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'initializeForm',
        () async {
          setState(() {
            _errorMessage = null;
          });
          // If editing an existing payment
          if (widget.payment != null) {
            _amountController.text = widget.payment!.amountReceived.toString();
            _dateReceived = widget.payment!.paymentDate;
            _paymentMethod = widget.payment!.paymentMethod;
            _selectedJobId = widget.payment!.jobId;
            _selectedInvoiceId = widget.payment!.invoiceId;
            _notesController.text = widget.payment!.notes ?? '';
            _voiceNoteUrl = widget.payment!.voiceNoteUrl;
          } else {
            // If creating a new payment with pre-selected job or invoice
            _selectedJobId = widget.jobId;
            _selectedInvoiceId = widget.invoiceId;
          }

          // Load jobs for dropdown
          final jobs = await _supabaseService.getJobs();
          if (!mounted) return;

          // Load invoices based on selected job or all invoices if no job selected
          List<Invoice> invoices;
          if (_selectedJobId != null) {
            invoices = await _supabaseService.getInvoicesByJob(_selectedJobId!);
          } else {
            invoices = await _supabaseService.getInvoices();
          }
          if (!mounted) return;

          // If invoice is selected, get its details
          if (_selectedInvoiceId != null) {
            try {
              final invoice = await _supabaseService.getInvoiceById(
                _selectedInvoiceId!,
              );
              _selectedInvoice = invoice;

              // If no amount entered yet, suggest the remaining balance
              if (_amountController.text.isEmpty && invoice != null) {
                // Calculate balance due
                final totalAmount = invoice.totalAmount;
                final amountPaid = invoice.amountPaid;
                final balanceDue = totalAmount - amountPaid;
                _amountController.text = balanceDue.toString();
              }

              // If invoice has a job, select it
              if (invoice != null && invoice.jobId.isNotEmpty) {
                _selectedJobId ??= invoice.jobId;
              }
            } catch (e) {
              // Handle case where invoice doesn't exist
              _selectedInvoiceId = null;
            }
          }
          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _invoices = invoices;
          });
        },
        message: 'Loading payment data...',
        errorMessage: 'Failed to load payment data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load payment data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _loadInvoicesForJob(String jobId) async {
    if (!mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadInvoicesForJob',
        () async {
          final invoices = await _supabaseService.getInvoicesByJob(jobId);
          if (!mounted) return;

          setState(() {
            _invoices = invoices;
          });
        },
        message: 'Loading invoices...',
        errorMessage: 'Failed to load invoices',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load invoices: ${e.toString()}';
        });
      }
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    if (!mounted) return;

    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      if (!mounted) return;

      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    if (!mounted) return;

    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      if (!mounted) return;

      setState(() {
        _isRecording = false;
      });

      // The recording path is set inside the voice recording service
      // We'll use the path directly when uploading

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Update form fields with extracted information
        if (!mounted) return;

        setState(() {
          if (extractedInfo.containsKey('description') &&
              _notesController.text.isEmpty) {
            _notesController.text = extractedInfo['description'];
          }

          if (extractedInfo.containsKey('amount') &&
              _amountController.text.isEmpty) {
            _amountController.text = extractedInfo['amount'];
          }

          if (extractedInfo.containsKey('date')) {
            _dateReceived = extractedInfo['date'];
          }

          if (extractedInfo.containsKey('jobName') && _selectedJobId == null) {
            // Try to find a job with a matching name
            final jobName = extractedInfo['jobName'];
            Job? matchingJob;
            try {
              matchingJob = _jobs.firstWhere(
                (job) =>
                    job.title.toLowerCase().contains(jobName.toLowerCase()),
              );
            } catch (e) {
              // No matching job found
              matchingJob = _jobs.isNotEmpty ? _jobs.first : null;
            }

            if (matchingJob != null) {
              _selectedJobId = matchingJob.id;
              // Load invoices for this job
              _loadInvoicesForJob(matchingJob.id);
            }
          }
        });

        // Upload the audio file to Supabase Storage
        final recordId = widget.payment?.id ?? const Uuid().v4();
        final recordingPath = _voiceRecordingService.getRecordingPath();
        if (recordingPath != null && recordingPath.isNotEmpty) {
          _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
            recordingPath,
            'payments',
            recordId,
          );
        }
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  Future<void> _savePayment() async {
    if (!mounted || !_formKey.currentState!.validate()) return;

    final amount = double.parse(_amountController.text);

    // Ensure we have a job ID
    if (_selectedJobId == null) {
      ErrorDisplay.showWarning(context, 'Please select a job');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'savePayment',
        () async {
          // Check if we're online
          final isOnline = await _dataRepository.isOnline();
          if (!mounted) return;

          // Create or update payment
          final userId = Supabase.instance.client.auth.currentUser!.id;

          if (widget.payment == null) {
            // Create new payment
            final newPayment = Payment(
              userId: userId,
              jobId: _selectedJobId!,
              invoiceId: _selectedInvoiceId,
              amountReceived: amount,
              paymentDate: _dateReceived,
              paymentMethod: _paymentMethod,
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              voiceNoteUrl: _voiceNoteUrl,
            );

            // Add payment to DataRepository (handles both online and offline)
            await _dataRepository.addPayment(newPayment);
          } else {
            // Update existing payment
            final updatedPayment = widget.payment!.copyWith(
              jobId: _selectedJobId,
              invoiceId: _selectedInvoiceId,
              amountReceived: amount,
              paymentDate: _dateReceived,
              paymentMethod: _paymentMethod,
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              voiceNoteUrl: _voiceNoteUrl ?? widget.payment!.voiceNoteUrl,
            );

            // Add payment to DataRepository (handles both online and offline)
            await _dataRepository.addPayment(updatedPayment);
          }
          if (!mounted) return;

          // If payment is linked to an invoice and we're online, update the invoice's amount paid and status
          if (_selectedInvoiceId != null && isOnline) {
            final invoice = await _dataRepository.getInvoiceById(
              _selectedInvoiceId!,
            );
            if (!mounted) return;

            if (invoice != null) {
              // Calculate new amount paid (considering if this is an update to an existing payment)
              double newAmountPaid = invoice.amountPaid;

              if (widget.payment != null) {
                // If updating, subtract the old amount and add the new amount
                newAmountPaid =
                    newAmountPaid - widget.payment!.amountReceived + amount;
              } else {
                // If creating, just add the new amount
                newAmountPaid = newAmountPaid + amount;
              }

              // Determine new status based on amount paid
              String newStatus;
              if (newAmountPaid >= invoice.totalAmount) {
                newStatus = 'paid';
              } else if (DateTime.now().isAfter(invoice.dueDate)) {
                newStatus = 'overdue';
              } else {
                newStatus = 'open';
              }

              // Update the invoice
              final updatedInvoice = invoice.copyWith(
                amountPaid: newAmountPaid,
                status: newStatus,
              );

              await _dataRepository.addInvoice(updatedInvoice);
              if (!mounted) return;
            }
          }

          if (mounted) {
            // Show success feedback using centralized system
            final operation = widget.payment == null ? 'save' : 'update';
            ErrorDisplay.showDataOperation(
              context,
              'payment',
              operation,
              isOffline: !isOnline,
            );
            Navigator.pop(context);
          }
        },
        message:
            widget.payment == null
                ? 'Creating payment...'
                : 'Updating payment...',
        errorMessage: 'Failed to save payment',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'savePayment',
            'isEditing': widget.payment != null,
            'paymentId': widget.payment?.id,
            'jobId': _selectedJobId,
            'invoiceId': _selectedInvoiceId,
            'amount': _amountController.text,
          },
        );
        ErrorHandler.logError(appError);

        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _savePayment(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(
          widget.payment == null ? 'Add Payment' : 'Edit Payment',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          if (loadingProvider.isLoading('initializeForm') ||
              loadingProvider.isLoading('loadInvoicesForJob')) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_errorMessage != null) {
            return Center(
              child: ErrorDisplay.buildErrorArea(
                AppError(
                  type: ErrorType.unknown,
                  severity: ErrorSeverity.medium,
                  message: _errorMessage!,
                  userFriendlyMessage: _errorMessage!,
                  context: {
                    'screen': 'PaymentFormScreen',
                    'operation': 'displayError',
                  },
                ),
                onRetry: () => _initializeForm(),
                onDismiss: () => setState(() => _errorMessage = null),
              ),
            );
          }

          return ResponsiveLayout(
            child: SingleChildScrollView(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Offline status indicator
                    if (_isOffline)
                      Container(
                        padding: spacing.ResponsiveSpacing.getPadding(
                          context,
                          base: 8.0,
                        ),
                        margin: EdgeInsets.only(
                          bottom: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: 16.0,
                          ),
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withAlpha(51), // 0.2 * 255 = 51
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(
                              context,
                              base: 8.0,
                            ),
                          ),
                          border: Border.all(color: Colors.orange),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.cloud_off,
                              color: Colors.orange,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                              ),
                            ),
                            SizedBox(
                              width: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8.0,
                              ),
                            ),
                            Expanded(
                              child: ResponsiveBody(
                                'You are currently offline. Your changes will be saved locally and synced when connection is restored.',
                                style: TextStyle(color: Colors.orange.shade800),
                              ),
                            ),
                          ],
                        ),
                      ),
                    // Job selection dropdown
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Job',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedJobId,
                      items:
                          _jobs.map((job) {
                            return DropdownMenuItem<String>(
                              value: job.id,
                              child: Text(job.title),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedJobId = value;
                          _selectedInvoiceId =
                              null; // Reset invoice when job changes
                        });
                        if (value != null) {
                          _loadInvoicesForJob(value);
                        }
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a job';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),

                    // Invoice selection dropdown
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Invoice (Optional)',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedInvoiceId,
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('No Invoice (General Payment)'),
                        ),
                        ..._invoices.map((invoice) {
                          return DropdownMenuItem<String>(
                            value: invoice.id,
                            child: Text(
                              'Invoice #${invoice.id != null && invoice.id!.length >= 8 ? invoice.id!.substring(0, 8) : "Unknown"} - \$${invoice.totalAmount.toStringAsFixed(2)}',
                            ),
                          );
                        }),
                      ],
                      onChanged: (value) async {
                        setState(() {
                          _selectedInvoiceId = value;
                        });

                        if (value != null) {
                          try {
                            final invoice = await _supabaseService
                                .getInvoiceById(value);
                            setState(() {
                              _selectedInvoice = invoice;
                              // Suggest the remaining balance as the payment amount
                              if (invoice != null) {
                                // Calculate balance due
                                final totalAmount = invoice.totalAmount;
                                final amountPaid = invoice.amountPaid;
                                final balanceDue = totalAmount - amountPaid;
                                _amountController.text = balanceDue.toString();
                              }
                            });
                          } catch (e) {
                            // Handle error
                          }
                        }
                      },
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),

                    // Payment amount
                    TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'Amount',
                        prefixText: '\$',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an amount';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        if (double.parse(value) <= 0) {
                          return 'Amount must be greater than zero';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),

                    // Date received
                    InkWell(
                      onTap: () async {
                        final pickedDate = await showDatePicker(
                          context: context,
                          initialDate: _dateReceived,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (pickedDate != null) {
                          setState(() {
                            _dateReceived = pickedDate;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Date Received',
                          border: OutlineInputBorder(),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ResponsiveBody(
                              DateFormat('MM/dd/yyyy').format(_dateReceived),
                            ),
                            Icon(
                              Icons.calendar_today,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),

                    // Payment method
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Payment Method',
                        border: OutlineInputBorder(),
                      ),
                      value: _paymentMethod,
                      items: const [
                        DropdownMenuItem(value: 'Cash', child: Text('Cash')),
                        DropdownMenuItem(value: 'Check', child: Text('Check')),
                        DropdownMenuItem(
                          value: 'Credit Card',
                          child: Text('Credit Card'),
                        ),
                        DropdownMenuItem(
                          value: 'Bank Transfer',
                          child: Text('Bank Transfer'),
                        ),
                        DropdownMenuItem(
                          value: 'PayPal',
                          child: Text('PayPal'),
                        ),
                        DropdownMenuItem(value: 'Venmo', child: Text('Venmo')),
                        DropdownMenuItem(value: 'Other', child: Text('Other')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _paymentMethod = value!;
                        });
                      },
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),

                    // Notes field with voice recording button
                    Stack(
                      alignment: Alignment.centerRight,
                      children: [
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes (Optional)',
                            border: const OutlineInputBorder(),
                            alignLabelWithHint: true,
                            suffixIcon:
                                _isRecording
                                    ? Container(
                                      margin: const EdgeInsets.only(right: 32),
                                      child: const Icon(
                                        Icons.mic,
                                        color: Colors.red,
                                      ),
                                    )
                                    : null,
                          ),
                          maxLines: 3,
                        ),
                        Positioned(
                          right: 8,
                          child: IconButton(
                            icon: Icon(
                              _isRecording ? Icons.stop : Icons.mic,
                              color:
                                  _isRecording
                                      ? Colors.red
                                      : Theme.of(context).colorScheme.primary,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                              ),
                            ),
                            onPressed:
                                _isRecording ? _stopRecording : _startRecording,
                            tooltip:
                                _isRecording
                                    ? 'Stop recording'
                                    : 'Record voice note',
                          ),
                        ),
                      ],
                    ),
                    if (_isRecording)
                      Padding(
                        padding: EdgeInsets.only(
                          top: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: 8.0,
                          ),
                        ),
                        child: ResponsiveLabel(
                          'Recording... Speak clearly to add payment details.',
                          style: const TextStyle(
                            color: Colors.red,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 24,
                      ),
                    ),

                    // Invoice summary if an invoice is selected
                    if (_selectedInvoice != null) ...[
                      Card(
                        child: Padding(
                          padding: spacing.ResponsiveSpacing.getPadding(
                            context,
                            base: 16,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ResponsiveSubtitle(
                                'Invoice Summary',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(
                                height: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: 8,
                                ),
                              ),
                              ResponsiveBody(
                                'Total Amount: \$${_selectedInvoice!.totalAmount.toStringAsFixed(2)}',
                              ),
                              ResponsiveBody(
                                'Amount Paid: \$${_selectedInvoice!.amountPaid.toStringAsFixed(2)}',
                              ),
                              ResponsiveBody(
                                'Balance Due: \$${_selectedInvoice!.balanceDue.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      _selectedInvoice!.balanceDue > 0
                                          ? Colors.red
                                          : Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                    ],

                    // Submit button
                    Consumer<LoadingStateProvider>(
                      builder: (context, loadingProvider, child) {
                        return SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed:
                                loadingProvider.isLoading('savePayment')
                                    ? null
                                    : _savePayment,
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(
                                double.infinity,
                                spacing.ResponsiveSpacing.getButtonHeight(
                                  context,
                                ),
                              ),
                            ),
                            child:
                                loadingProvider.isLoading('savePayment')
                                    ? CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: spacing
                                          .ResponsiveSpacing.getIconSize(
                                        context,
                                        base: 2,
                                      ),
                                    )
                                    : ResponsiveBody(
                                      widget.payment == null
                                          ? 'Add Payment'
                                          : 'Update Payment',
                                    ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
