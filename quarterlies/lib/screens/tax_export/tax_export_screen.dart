import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/models/tax_export_data.dart' as tax_models;
import 'package:quarterlies/services/tax_export_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class TaxExportScreen extends StatefulWidget {
  const TaxExportScreen({super.key});

  @override
  State<TaxExportScreen> createState() => _TaxExportScreenState();
}

class _TaxExportScreenState extends State<TaxExportScreen> {
  final TaxExportService _taxExportService = TaxExportService();

  TaxExportPeriod _selectedPeriod = TaxExportPeriod.currentYear;
  TaxExportFormat _selectedFormat = TaxExportFormat.both;
  DateTime? _customStartDate;
  DateTime? _customEndDate;

  bool _isExporting = false;
  String? _errorMessage;
  TaxExportData? _previewData;

  @override
  void initState() {
    super.initState();
    _loadPreview();
  }

  Future<void> _loadPreview() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTaxPreview',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final dateRange = _getSelectedDateRange();
          final taxData = await _taxExportService.generateTaxExportData(
            startDate: dateRange.startDate,
            endDate: dateRange.endDate,
            periodDescription: _selectedPeriod.displayName,
          );

          if (!mounted) return;

          setState(() {
            _previewData = taxData;
          });
        },
        message: 'Loading tax data preview...',
        errorMessage: 'Failed to load tax data preview',
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'loadTaxExportPreview',
          'selectedPeriod': _selectedPeriod.displayName,
          'customStartDate': _customStartDate?.toIso8601String(),
          'customEndDate': _customEndDate?.toIso8601String(),
        },
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });
      }
    }
  }

  DateRange _getSelectedDateRange() {
    if (_selectedPeriod == TaxExportPeriod.custom) {
      return DateRange(
        startDate:
            _customStartDate ??
            DateTime.now().subtract(const Duration(days: 365)),
        endDate: _customEndDate ?? DateTime.now(),
      );
    }
    // Convert tax_models.DateRange to report_models.DateRange
    final taxDateRange = _selectedPeriod.getDateRange();
    return DateRange(startDate: taxDateRange.start, endDate: taxDateRange.end);
  }

  Future<void> _exportData() async {
    if (_previewData == null) return;

    setState(() {
      _isExporting = true;
      _errorMessage = null;
    });

    try {
      final filePaths = <String>[];

      // Export based on selected format
      switch (_selectedFormat) {
        case TaxExportFormat.csv:
          final csvPath = await _taxExportService.exportToCsv(_previewData!);
          filePaths.add(csvPath);
          break;
        case TaxExportFormat.pdf:
          final pdfPath = await _taxExportService.exportToPdf(_previewData!);
          filePaths.add(pdfPath);
          break;
        case TaxExportFormat.both:
          final csvPath = await _taxExportService.exportToCsv(_previewData!);
          final pdfPath = await _taxExportService.exportToPdf(_previewData!);
          filePaths.addAll([csvPath, pdfPath]);
          break;
      }

      // Share the files
      await _taxExportService.shareExportedFiles(
        filePaths: filePaths,
        periodDescription: _previewData!.periodDescription,
      );

      if (mounted) {
        ErrorDisplay.showOperation(context, 'Tax data exported successfully');
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'exportTaxData',
          'selectedFormat': _selectedFormat.toString(),
          'periodDescription': _previewData?.periodDescription,
        },
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _exportData(),
        );
      }
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle('Tax Data Export'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: SingleChildScrollView(
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Period Selection Section
                  AdaptiveDetailSection(
                    title: 'Time Period',
                    icon: Icons.date_range,
                    children: [
                      _buildPeriodSelection(displayProvider),
                      if (_selectedPeriod == TaxExportPeriod.custom) ...[
                        SizedBox(
                          height: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                        ),
                        _buildCustomDateRange(displayProvider),
                      ],
                    ],
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: displayProvider.isOfficeMode ? 16 : 20,
                    ),
                  ),

                  // Format Selection Section
                  AdaptiveDetailSection(
                    title: 'Export Format',
                    icon: Icons.file_download,
                    children: [_buildFormatSelection(displayProvider)],
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: displayProvider.isOfficeMode ? 16 : 20,
                    ),
                  ),

                  // Preview Section
                  Consumer<LoadingStateProvider>(
                    builder: (context, loadingProvider, child) {
                      final isLoadingPreview = loadingProvider.isLoading(
                        'loadTaxPreview',
                      );

                      if (isLoadingPreview) {
                        return const Center(
                          child: QuarterliesLoadingIndicator(
                            message: 'Loading tax data preview...',
                            size: 32.0,
                          ),
                        );
                      } else if (_previewData != null) {
                        return AdaptiveDetailSection(
                          title: 'Preview',
                          icon: Icons.preview,
                          children: [_buildPreviewSummary(displayProvider)],
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: displayProvider.isOfficeMode ? 16 : 20,
                    ),
                  ),

                  // Export Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed:
                          _isExporting || _previewData == null
                              ? null
                              : _exportData,
                      icon:
                          _isExporting
                              ? SizedBox(
                                width: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 16,
                                ),
                                height: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 16,
                                ),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.onPrimary,
                                  ),
                                ),
                              )
                              : Icon(
                                Icons.file_download,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                ),
                              ),
                      label: ResponsiveBody(
                        _isExporting ? 'Exporting...' : 'Export Tax Data',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        minimumSize: Size(
                          double.infinity,
                          spacing.ResponsiveSpacing.getButtonHeight(context),
                        ),
                      ),
                    ),
                  ),

                  // Error Message
                  if (_errorMessage != null) ...[
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: displayProvider.isOfficeMode ? 12 : 16,
                      ),
                    ),
                    ErrorDisplay.buildErrorArea(
                      AppError(
                        type: ErrorType.unknown,
                        severity: ErrorSeverity.medium,
                        message: _errorMessage!,
                        userFriendlyMessage: _errorMessage!,
                        context: {
                          'screen': 'TaxExportScreen',
                          'operation': 'displayError',
                        },
                      ),
                      onRetry: () => _loadPreview(),
                      onDismiss: () => setState(() => _errorMessage = null),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPeriodSelection(DisplaySettingsProvider displayProvider) {
    return Column(
      children:
          TaxExportPeriod.values.map((period) {
            return RadioListTile<TaxExportPeriod>(
              title: ResponsiveBody(period.displayName),
              value: period,
              groupValue: _selectedPeriod,
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                });
                _loadPreview();
              },
              contentPadding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: displayProvider.isOfficeMode ? 8.0 : 12.0,
              ),
              dense: displayProvider.isOfficeMode,
            );
          }).toList(),
    );
  }

  Widget _buildCustomDateRange(DisplaySettingsProvider displayProvider) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: 'Start Date',
                date: _customStartDate,
                onTap: () => _selectDate(true),
                displayProvider: displayProvider,
              ),
            ),
            SizedBox(
              width: spacing.ResponsiveSpacing.getSpacing(
                context,
                base: displayProvider.isOfficeMode ? 12 : 16,
              ),
            ),
            Expanded(
              child: _buildDateField(
                label: 'End Date',
                date: _customEndDate,
                onTap: () => _selectDate(false),
                displayProvider: displayProvider,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required DisplaySettingsProvider displayProvider,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(),
          suffixIcon: Icon(
            Icons.calendar_today,
            size: spacing.ResponsiveSpacing.getIconSize(context),
          ),
          contentPadding: spacing.ResponsiveSpacing.getPadding(
            context,
            base: displayProvider.isOfficeMode ? 12.0 : 16.0,
          ),
        ),
        child: ResponsiveBody(
          date != null
              ? DateFormat('MMM dd, yyyy').format(date)
              : 'Select date',
          style: TextStyle(color: date != null ? null : Colors.grey),
        ),
      ),
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final initialDate =
        isStartDate
            ? (_customStartDate ??
                DateTime.now().subtract(const Duration(days: 365)))
            : (_customEndDate ?? DateTime.now());

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _customStartDate = selectedDate;
        } else {
          _customEndDate = selectedDate;
        }
      });
      _loadPreview();
    }
  }

  Widget _buildFormatSelection(DisplaySettingsProvider displayProvider) {
    return Column(
      children: [
        RadioListTile<TaxExportFormat>(
          title: ResponsiveBody('CSV Only'),
          subtitle: ResponsiveLabel('Spreadsheet format for data analysis'),
          value: TaxExportFormat.csv,
          groupValue: _selectedFormat,
          onChanged: (value) {
            setState(() {
              _selectedFormat = value!;
            });
          },
          contentPadding: spacing.ResponsiveSpacing.getPadding(
            context,
            base: displayProvider.isOfficeMode ? 8.0 : 12.0,
          ),
          dense: displayProvider.isOfficeMode,
        ),
        RadioListTile<TaxExportFormat>(
          title: ResponsiveBody('PDF Only'),
          subtitle: ResponsiveLabel('Formatted report for printing'),
          value: TaxExportFormat.pdf,
          groupValue: _selectedFormat,
          onChanged: (value) {
            setState(() {
              _selectedFormat = value!;
            });
          },
          contentPadding: spacing.ResponsiveSpacing.getPadding(
            context,
            base: displayProvider.isOfficeMode ? 8.0 : 12.0,
          ),
          dense: displayProvider.isOfficeMode,
        ),
        RadioListTile<TaxExportFormat>(
          title: ResponsiveBody('Both CSV and PDF'),
          subtitle: ResponsiveLabel('Complete export package'),
          value: TaxExportFormat.both,
          groupValue: _selectedFormat,
          onChanged: (value) {
            setState(() {
              _selectedFormat = value!;
            });
          },
          contentPadding: spacing.ResponsiveSpacing.getPadding(
            context,
            base: displayProvider.isOfficeMode ? 8.0 : 12.0,
          ),
          dense: displayProvider.isOfficeMode,
        ),
      ],
    );
  }

  Widget _buildPreviewSummary(DisplaySettingsProvider displayProvider) {
    if (_previewData == null) return const SizedBox.shrink();

    final summary = _previewData!.summary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveSubtitle('Period: ${_previewData!.periodDescription}'),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(
            context,
            base: displayProvider.isOfficeMode ? 12 : 16,
          ),
        ),

        _buildSummaryRow('Total Income', summary.totalIncome, displayProvider),
        _buildSummaryRow(
          'Total Expenses',
          summary.totalExpenses,
          displayProvider,
        ),
        _buildSummaryRow(
          'Mileage Deductions',
          summary.totalMileageDeduction,
          displayProvider,
        ),
        _buildSummaryRow(
          'Net Profit',
          summary.netProfit,
          displayProvider,
          isTotal: true,
        ),

        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(
            context,
            base: displayProvider.isOfficeMode ? 8 : 12,
          ),
        ),

        _buildSummaryRow(
          'Estimated Tax (25%)',
          summary.estimatedTaxOwed,
          displayProvider,
        ),
        _buildSummaryRow(
          'Tax Payments Made',
          summary.totalTaxPayments,
          displayProvider,
        ),
        _buildSummaryRow(
          'Tax Balance',
          summary.taxBalance,
          displayProvider,
          isTotal: true,
          color: summary.taxBalance > 0 ? Colors.red : Colors.green,
        ),

        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(
            context,
            base: displayProvider.isOfficeMode ? 12 : 16,
          ),
        ),

        ResponsiveLabel(
          'Data Summary:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(
            context,
            base: displayProvider.isOfficeMode ? 4 : 8,
          ),
        ),
        ResponsiveLabel(
          '• ${_previewData!.incomeItems.length} income transactions\n'
          '• ${_previewData!.expenseItems.length} expense records\n'
          '• ${_previewData!.mileageItems.length} mileage entries\n'
          '• ${_previewData!.taxPaymentItems.length} tax payments',
          style: TextStyle(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(
    String label,
    double amount,
    DisplaySettingsProvider displayProvider, {
    bool isTotal = false,
    Color? color,
  }) {
    return Padding(
      padding: spacing.ResponsiveSpacing.getPadding(
        context,
        base: displayProvider.isOfficeMode ? 2.0 : 4.0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveLabel(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          ResponsiveLabel(
            NumberFormat.currency(symbol: '\$').format(amount),
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
