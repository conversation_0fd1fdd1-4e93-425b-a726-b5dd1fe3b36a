import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class CreateSigningRequestScreen extends StatefulWidget {
  final String documentType; // 'estimate' or 'contract'
  final String documentId;
  final Uint8List documentBytes;
  final Customer customer;
  final Job job;

  const CreateSigningRequestScreen({
    super.key,
    required this.documentType,
    required this.documentId,
    required this.documentBytes,
    required this.customer,
    required this.job,
  });

  @override
  State<CreateSigningRequestScreen> createState() =>
      _CreateSigningRequestScreenState();
}

class _CreateSigningRequestScreenState
    extends State<CreateSigningRequestScreen> {
  String? _errorMessage;

  final DocumentSigningService _documentSigningService =
      DocumentSigningService();

  @override
  void initState() {
    super.initState();
    // Automatically create the signing request when the screen loads
    _createSigningRequest();
  }

  Future<void> _createSigningRequest() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'createSigningRequest',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Create the signing request with automatic email sending
          await _documentSigningService.createSigningRequest(
            pdfBytes: widget.documentBytes,
            documentType: widget.documentType,
            documentId: widget.documentId,
            customerId: widget.customer.id,
            customerEmail: widget.customer.email ?? '',
            customerName: widget.customer.name,
            job: widget.job,
          );

          // Show email sent feedback
          if (mounted) {
            ErrorDisplay.showOperation(context, FeedbackMessages.emailSent);
          }
        },
        message: 'Sending ${widget.documentType} for signature...',
        errorMessage: 'Failed to create signing request',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error creating signing request: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DisplaySettingsProvider, LoadingStateProvider>(
      builder: (context, displayProvider, loadingProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle(
              'Sending ${widget.documentType.toUpperCase()} for Signature',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: Padding(
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: 16.0,
              ),
              child: _buildContent(displayProvider, loadingProvider),
            ),
          ),
        );
      },
    );
  }

  Widget _buildContent(
    DisplaySettingsProvider displayProvider,
    LoadingStateProvider loadingProvider,
  ) {
    final isLoading = loadingProvider.isLoading('createSigningRequest');

    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            QuarterliesLoadingIndicator(
              message:
                  'Sending ${widget.documentType} for electronic signature...',
              size: 32.0,
              showOfflineStatus: true,
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
            ),
            ResponsiveBody(
              'Please wait while we prepare your document for signing...',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
            ),
            ResponsiveTitle(
              'Error sending signature request',
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
            ),
            ResponsiveBody(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
            ),
            ElevatedButton(
              onPressed: _createSigningRequest,
              style: ElevatedButton.styleFrom(
                padding: spacing.ResponsiveSpacing.getPadding(
                  context,
                  base: 16.0,
                ),
                minimumSize: Size(
                  0,
                  spacing.ResponsiveSpacing.getButtonHeight(context),
                ),
              ),
              child: ResponsiveBody('Try Again'),
            ),
          ],
        ),
      );
    }

    // Success view
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          ResponsiveTitle(
            '${widget.documentType.toUpperCase()} Sent Successfully',
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          ResponsiveBody(
            'An email has been sent to ${widget.customer.email} with a link to sign the ${widget.documentType}.',
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          ResponsiveSubtitle(
            'What happens next?',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
          ),
          Container(
            padding: spacing.ResponsiveSpacing.getPadding(context, base: 16.0),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(
                spacing.ResponsiveSpacing.getBorderRadius(context, base: 8),
              ),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue.shade700,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 20,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 8,
                      ),
                    ),
                    ResponsiveSubtitle(
                      'Electronic Signature Process',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 12,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveLabel('1. '),
                    Expanded(
                      child: ResponsiveLabel(
                        'Your customer will receive an email with signing instructions and a secure link',
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 8,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveLabel('2. '),
                    Expanded(
                      child: ResponsiveLabel(
                        'They can review the document and sign it electronically with a legally binding signature',
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 8,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveLabel('3. '),
                    Expanded(
                      child: ResponsiveLabel(
                        'A certification document will be generated to verify the authenticity of the signature',
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 8,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveLabel('4. '),
                    Expanded(
                      child: ResponsiveLabel(
                        'Both you and your customer will receive email confirmations with the signed document',
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 8,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveLabel('5. '),
                    Expanded(
                      child: ResponsiveLabel(
                        'The signed document with certification will be available in your account',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: 16.0,
              ),
              minimumSize: Size(
                0,
                spacing.ResponsiveSpacing.getButtonHeight(context),
              ),
            ),
            child: ResponsiveBody('Done'),
          ),
        ],
      ),
    );
  }
}
