import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:signature/signature.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class DocumentSigningScreen extends StatefulWidget {
  final String signingId;

  const DocumentSigningScreen({super.key, required this.signingId});

  @override
  State<DocumentSigningScreen> createState() => _DocumentSigningScreenState();
}

class _DocumentSigningScreenState extends State<DocumentSigningScreen> {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final DocumentSigningService _documentSigningService =
      DocumentSigningService();
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  bool _isSigned = false;
  String? _errorMessage;
  Map<String, dynamic>? _signingRequest;
  String? _pdfPath;
  int _totalPages = 0;
  int _currentPage = 0;

  // Signature controller
  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.black,
    exportBackgroundColor: Colors.transparent,
  );

  @override
  void initState() {
    super.initState();
    _loadSigningRequest();
  }

  Future<void> _loadSigningRequest() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadSigningRequest',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get the signing request from the database
          final response =
              await _supabaseClient
                  .from('document_signing_requests')
                  .select()
                  .eq('id', widget.signingId)
                  .limit(1)
                  .maybeSingle();

          if (response == null) {
            setState(() {
              _errorMessage = 'Signing request not found';
            });
            return;
          }

          // Check if the document is already signed
          if (response['status'] == 'signed') {
            setState(() {
              _isSigned = true;
              _signingRequest = response;
            });
            return;
          }

          // Check if the request is expired
          final expiresAt = DateTime.parse(response['expires_at']);
          if (expiresAt.isBefore(DateTime.now())) {
            setState(() {
              _errorMessage = 'This signing request has expired';
            });
            return;
          }

          // Update the status to 'viewed' if it's currently 'pending'
          if (response['status'] == 'pending') {
            await _supabaseClient
                .from('document_signing_requests')
                .update({
                  'status': 'viewed',
                  'updated_at': DateTime.now().toIso8601String(),
                })
                .eq('id', widget.signingId);
          }

          // Store the signing request
          setState(() {
            _signingRequest = response;
          });

          // Download the PDF
          await _downloadPdf(response['document_url']);
        },
        message: 'Loading document...',
        errorMessage: 'Failed to load document',
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'loadSigningRequest',
          'signingId': widget.signingId,
        },
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _downloadPdf(String url) async {
    try {
      // Download the PDF from the URL
      final response = await http.get(Uri.parse(url));

      if (response.statusCode != 200) {
        setState(() {
          _errorMessage = 'Failed to download document: ${response.statusCode}';
        });
        return;
      }

      // Save the PDF to a temporary file
      final tempDir = await getTemporaryDirectory();
      final filePath =
          '${tempDir.path}/document_${DateTime.now().millisecondsSinceEpoch}.pdf';

      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);

      setState(() {
        _pdfPath = filePath;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error downloading document: $e';
      });
    }
  }

  Future<void> _signDocument() async {
    if (_signatureController.isEmpty) {
      ErrorDisplay.showWarning(context, 'Please provide a signature');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'signDocument',
        () async {
          // Get the signature as a PNG image
          final signatureImage = await _signatureController.toPngBytes();
          if (signatureImage == null) {
            throw Exception('Failed to create signature image');
          }

          // Get device information for the certification
          String deviceInfo = 'Unknown Device';
          String ipAddress = '0.0.0.0';

          try {
            if (kIsWeb) {
              final webInfo = await _deviceInfoPlugin.webBrowserInfo;
              deviceInfo = '${webInfo.browserName} on ${webInfo.platform}';
            } else if (Platform.isAndroid) {
              final androidInfo = await _deviceInfoPlugin.androidInfo;
              deviceInfo = '${androidInfo.brand} ${androidInfo.model}';
            } else if (Platform.isIOS) {
              final iosInfo = await _deviceInfoPlugin.iosInfo;
              deviceInfo = '${iosInfo.name} ${iosInfo.systemVersion}';
            }

            // Get IP address (in a real app, you would use a service to get the actual IP)
            final ipResponse = await http.get(
              Uri.parse('https://api.ipify.org'),
            );
            if (ipResponse.statusCode == 200) {
              ipAddress = ipResponse.body;
            }
          } catch (e) {
            debugPrint('Error getting device info: $e');
          }

          // Use the document signing service to handle the signing process
          final success = await _documentSigningService.handleDocumentSigning(
            signingRequestId: widget.signingId,
            signatureBytes: signatureImage,
            ipAddress: ipAddress,
            deviceInfo: deviceInfo,
          );

          if (success) {
            setState(() {
              _isSigned = true;
            });

            // Show success message
            if (mounted) {
              ErrorDisplay.showSuccess(context, 'Document signed successfully');
            }
          } else {
            throw Exception('Failed to sign document');
          }
        },
        message: 'Signing document...',
        errorMessage: 'Failed to sign document',
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'signDocument',
          'signingId': widget.signingId,
          'hasSignature': _signatureController.isNotEmpty,
        },
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showErrorDialog(
          context,
          appError,
          onRetry: () => _signDocument(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DisplaySettingsProvider, LoadingStateProvider>(
      builder: (context, displayProvider, loadingProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle(
              _signingRequest != null
                  ? '${_signingRequest!['document_type'].toString().toUpperCase()} #${_signingRequest!['document_id'].toString().substring(0, 8)}'
                  : 'Document Signing',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: _buildBody(displayProvider, loadingProvider),
          ),
        );
      },
    );
  }

  Widget _buildBody(
    DisplaySettingsProvider displayProvider,
    LoadingStateProvider loadingProvider,
  ) {
    if (loadingProvider.isLoading('loadSigningRequest')) {
      return const Center(
        child: QuarterliesLoadingIndicator(message: 'Loading document...'),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 48),
              ),
              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
              ),
              ResponsiveBody(
                _errorMessage!,
                style: TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (_isSigned) {
      return Center(
        child: Padding(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 48),
              ),
              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
              ),
              ResponsiveTitle(
                'Document Signed Successfully',
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
              ),
              ResponsiveBody(
                'Thank you for signing this document.',
                style: TextStyle(color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // PDF Viewer
        Expanded(
          flex: 2,
          child:
              _pdfPath == null
                  ? const Center(child: Text('Document not available'))
                  : PDFView(
                    filePath: _pdfPath!,
                    enableSwipe: true,
                    swipeHorizontal: true,
                    autoSpacing: true,
                    pageFling: true,
                    pageSnap: true,
                    onRender: (pages) {
                      setState(() {
                        _totalPages = pages!;
                      });
                    },
                    onPageChanged: (page, total) {
                      setState(() {
                        _currentPage = page!;
                      });
                    },
                    onError: (error) {
                      final appError = AppError.fromException(
                        Exception(error.toString()),
                        context: {
                          'operation': 'pdfViewError',
                          'pdfPath': _pdfPath,
                        },
                      );
                      ErrorHandler.logError(appError);

                      if (mounted) {
                        setState(() {
                          _errorMessage = ErrorHandler.getUserFriendlyMessage(
                            appError,
                          );
                        });
                      }
                    },
                  ),
        ),

        // Page indicator
        if (_totalPages > 0)
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: spacing.ResponsiveSpacing.getSpacing(
                context,
                base: 8.0,
              ),
            ),
            child: ResponsiveLabel('Page ${_currentPage + 1} of $_totalPages'),
          ),

        // Signature area
        Expanded(
          flex: 1,
          child: Consumer<DisplaySettingsProvider>(
            builder: (context, displayProvider, child) {
              return Padding(
                padding: spacing.ResponsiveSpacing.getPadding(
                  context,
                  base: 16.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ResponsiveSubtitle(
                      'Please sign below:',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 8,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(
                              context,
                              base: 4,
                            ),
                          ),
                        ),
                        child: Signature(
                          controller: _signatureController,
                          backgroundColor: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 8,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: _signatureController.clear,
                          child: ResponsiveLabel('Clear'),
                        ),
                        ElevatedButton(
                          onPressed:
                              loadingProvider.isLoading('signDocument')
                                  ? null
                                  : _signDocument,
                          style: ElevatedButton.styleFrom(
                            padding: spacing.ResponsiveSpacing.getPadding(
                              context,
                              base: 16.0,
                            ),
                            minimumSize: Size(
                              0,
                              spacing.ResponsiveSpacing.getButtonHeight(
                                context,
                              ),
                            ),
                          ),
                          child:
                              loadingProvider.isLoading('signDocument')
                                  ? SizedBox(
                                    width: spacing
                                        .ResponsiveSpacing.getIconSize(
                                      context,
                                      base: 20,
                                    ),
                                    height: spacing
                                        .ResponsiveSpacing.getIconSize(
                                      context,
                                      base: 20,
                                    ),
                                    child: const CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                  : ResponsiveLabel('Sign Document'),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _signatureController.dispose();
    // Clean up temporary file when done
    if (_pdfPath != null) {
      try {
        File(_pdfPath!).delete();
      } catch (e) {
        // Ignore errors on cleanup
      }
    }
    super.dispose();
  }
}
