import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class SettingsReviewStep extends StatefulWidget {
  final UserProfile userProfile;
  final UserSettings userSettings;
  final Function(UserProfile) onProfileUpdated;
  final Function(UserSettings) onSettingsUpdated;
  final VoidCallback onComplete;
  final VoidCallback onPrevious;
  final bool isLoading;

  const SettingsReviewStep({
    super.key,
    required this.userProfile,
    required this.userSettings,
    required this.onProfileUpdated,
    required this.onSettingsUpdated,
    required this.onComplete,
    required this.onPrevious,
    required this.isLoading,
  });

  @override
  State<SettingsReviewStep> createState() => _SettingsReviewStepState();
}

class _SettingsReviewStepState extends State<SettingsReviewStep> {
  late UserSettings _currentSettings;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.userSettings;
  }

  void _updateSetting<T>(T value, UserSettings Function(T) updater) {
    setState(() {
      _currentSettings = updater(value);
    });
    widget.onSettingsUpdated(_currentSettings);
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
      child: Padding(
        padding: spacing.ResponsiveSpacing.getPadding(context, base: 24.0),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    ResponsiveTitle(
                      'Review & Customize Settings',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 12.0,
                      ),
                    ),
                    ResponsiveBody(
                      'Review your information and customize default settings. You can change these anytime in Settings.',
                      style: TextStyle(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 32.0,
                      ),
                    ),

                    // Profile summary
                    AdaptiveFormSection(
                      title: 'Your Profile',
                      icon: Icons.person,
                      children: [
                        _buildInfoRow(
                          context,
                          'Name',
                          widget.userProfile.fullName.isNotEmpty
                              ? widget.userProfile.fullName
                              : 'Not provided',
                        ),
                        _buildInfoRow(
                          context,
                          'Email',
                          widget.userProfile.email ?? 'Not provided',
                        ),
                        if (widget.userProfile.phone?.isNotEmpty == true)
                          _buildInfoRow(
                            context,
                            'Phone',
                            widget.userProfile.phone!,
                          ),
                      ],
                    ),

                    // Business summary
                    if (widget.userSettings.businessName?.isNotEmpty == true ||
                        widget.userSettings.businessEmail?.isNotEmpty == true)
                      AdaptiveFormSection(
                        title: 'Business Information',
                        icon: Icons.business,
                        children: [
                          if (widget.userSettings.businessName?.isNotEmpty ==
                              true)
                            _buildInfoRow(
                              context,
                              'Business Name',
                              widget.userSettings.businessName!,
                            ),
                          if (widget.userSettings.businessEmail?.isNotEmpty ==
                              true)
                            _buildInfoRow(
                              context,
                              'Business Email',
                              widget.userSettings.businessEmail!,
                            ),
                        ],
                      ),

                    // App preferences
                    AdaptiveFormSection(
                      title: 'App Preferences',
                      icon: Icons.settings,
                      children: [
                        _buildSwitchTile(
                          context,
                          'Voice Input',
                          'Enable voice input for text fields',
                          _currentSettings.enableVoiceInput,
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              enableVoiceInput: val,
                            ),
                          ),
                        ),
                        _buildSwitchTile(
                          context,
                          'Offline Mode',
                          'Work offline with automatic sync',
                          _currentSettings.enableOfflineMode,
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              enableOfflineMode: val,
                            ),
                          ),
                        ),
                        _buildSwitchTile(
                          context,
                          'WiFi Only Sync',
                          'Only sync data when connected to WiFi',
                          _currentSettings.wifiOnlySync,
                          (value) => _updateSetting(
                            value,
                            (val) =>
                                _currentSettings.copyWith(wifiOnlySync: val),
                          ),
                        ),
                      ],
                    ),

                    // Display settings
                    AdaptiveFormSection(
                      title: 'Display Settings',
                      icon: Icons.display_settings,
                      children: [
                        _buildDisplayModeSelector(context),
                        _buildSwitchTile(
                          context,
                          'Enhanced Contrast',
                          'Better visibility in bright conditions',
                          _currentSettings.enhancedContrastMode,
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              enhancedContrastMode: val,
                            ),
                          ),
                        ),
                        _buildSwitchTile(
                          context,
                          'Dynamic Color Adjustment',
                          'Automatically adjust colors based on lighting',
                          _currentSettings.dynamicColorAdjustment,
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              dynamicColorAdjustment: val,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Invoice settings
                    AdaptiveFormSection(
                      title: 'Invoice Settings',
                      icon: Icons.receipt_long,
                      children: [
                        _buildDropdownTile(
                          context,
                          'Default Due Days',
                          'Default number of days for invoice due dates',
                          _currentSettings.defaultInvoiceDueDays,
                          [15, 30, 45, 60, 90],
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              defaultInvoiceDueDays: val,
                            ),
                          ),
                        ),
                        _buildSwitchTile(
                          context,
                          'Due Date Notifications',
                          'Get notified about upcoming invoice due dates',
                          _currentSettings.enableDueDateNotifications,
                          (value) => _updateSetting(
                            value,
                            (val) => _currentSettings.copyWith(
                              enableDueDateNotifications: val,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Completion note
                    Container(
                      margin: EdgeInsets.only(
                        top: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 24.0,
                        ),
                      ),
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16.0,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(
                          spacing.ResponsiveSpacing.getBorderRadius(
                            context,
                            base: 12,
                          ),
                        ),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            color: Theme.of(context).colorScheme.primary,
                            size: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 24,
                            ),
                          ),
                          SizedBox(
                            width: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12.0,
                            ),
                          ),
                          Expanded(
                            child: ResponsiveLabel(
                              'You\'re all set! Click "Complete Setup" to start using Quarterlies.',
                              style: TextStyle(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Navigation buttons
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 24.0),
            ),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: widget.isLoading ? null : widget.onPrevious,
                    style: OutlinedButton.styleFrom(
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          spacing.ResponsiveSpacing.getBorderRadius(
                            context,
                            base: 12,
                          ),
                        ),
                      ),
                    ),
                    child: ResponsiveBody('Back'),
                  ),
                ),
                SizedBox(
                  width: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 16.0,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: widget.isLoading ? null : widget.onComplete,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          spacing.ResponsiveSpacing.getBorderRadius(
                            context,
                            base: 12,
                          ),
                        ),
                      ),
                    ),
                    child:
                        widget.isLoading
                            ? SizedBox(
                              height: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 24,
                              ),
                              width: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 24,
                              ),
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : ResponsiveBody(
                              'Complete Setup',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 6.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: spacing.ResponsiveSpacing.getSpacing(context, base: 100),
            child: ResponsiveBody(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(child: ResponsiveBody(value)),
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveBody(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (subtitle.isNotEmpty) ...[
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 4.0,
                    ),
                  ),
                  ResponsiveLabel(
                    subtitle,
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile(
    BuildContext context,
    String title,
    String subtitle,
    int value,
    List<int> options,
    Function(int) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveBody(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (subtitle.isNotEmpty) ...[
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 4.0,
                    ),
                  ),
                  ResponsiveLabel(
                    subtitle,
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          DropdownButton<int>(
            value: value,
            onChanged: (newValue) {
              if (newValue != null) {
                onChanged(newValue);
              }
            },
            items:
                options.map((option) {
                  return DropdownMenuItem<int>(
                    value: option,
                    child: ResponsiveBody('$option days'),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayModeSelector(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveBody(
            'Display Mode',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          SizedBox(
            height: spacing.ResponsiveSpacing.getSpacing(context, base: 12.0),
          ),
          Row(
            children: [
              Expanded(
                child: _buildDisplayModeOption(
                  context,
                  'Field Mode',
                  'Large touch targets, ideal for field work',
                  DisplayMode.field,
                  Icons.touch_app,
                ),
              ),
              SizedBox(
                width: spacing.ResponsiveSpacing.getSpacing(
                  context,
                  base: 12.0,
                ),
              ),
              Expanded(
                child: _buildDisplayModeOption(
                  context,
                  'Office Mode',
                  'Compact layout, more information density',
                  DisplayMode.office,
                  Icons.desktop_windows,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayModeOption(
    BuildContext context,
    String title,
    String description,
    DisplayMode mode,
    IconData icon,
  ) {
    final isSelected = _currentSettings.displayMode == mode;

    return GestureDetector(
      onTap:
          () => _updateSetting(
            mode,
            (val) => _currentSettings.copyWith(displayMode: val),
          ),
      child: Container(
        padding: spacing.ResponsiveSpacing.getPadding(context, base: 16.0),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withValues(alpha: 0.3)
                  : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(
            spacing.ResponsiveSpacing.getBorderRadius(context, base: 12),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 32),
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 8.0),
            ),
            ResponsiveLabel(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 6.0),
            ),
            ResponsiveLabel(
              description,
              style: TextStyle(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }
}
