import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/screens/dashboard_screen.dart';
import 'package:quarterlies/screens/home_screen.dart';
import 'package:quarterlies/screens/jobs/job_list_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_list_screen.dart';
import 'package:quarterlies/screens/reports/index.dart';
import 'package:quarterlies/screens/document_signing/index.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/widgets/bottom_nav_bar.dart';
import 'package:quarterlies/widgets/offline_status_banner.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
// Import QuickAddButton from bottom_nav_bar.dart where it's defined

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  // List of screens to navigate between
  final List<Widget> _screens = [
    const DashboardScreen(),
    const HomeScreen(),
    const JobListScreen(),
    const InvoiceListScreen(),
    const ReportsScreen(),
    const SignedDocumentsScreen(),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Scaffold(
            body: ResponsiveLayout(
              child: OfflineStatusBanner(child: _screens[_currentIndex]),
            ),
            bottomNavigationBar: BottomNavBar(
              currentIndex: _currentIndex,
              onTap: _onTabTapped,
            ),
            floatingActionButton:
                displayProvider.isOfficeMode
                    ? null // Hide FAB in Office Mode for cleaner desktop experience
                    : const QuickAddButton(),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
          ),
        );
      },
    );
  }
}
